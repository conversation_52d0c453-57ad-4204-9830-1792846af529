import React, { useState } from 'react';
import { View, TextInput, Text } from 'react-native';

export default function GlobalChargesForm({ onChange }: any) {
    const [gst, setGst] = useState('');
    const [serviceCharge, setServiceCharge] = useState('');
    const [tip, setTip] = useState('');

    const handleChange = () => {
        onChange({
            gst: parseFloat(gst),
            serviceCharge: parseFloat(serviceCharge) || 0,
            tip: parseFloat(tip) || 0,
        });
    };

    return (
        <View style={{ marginVertical: 20 }}>
            <Text style={{ fontWeight: 'bold' }}>Global Charges</Text>
            <TextInput
                placeholder="Total GST"
                keyboardType="numeric"
                value={gst}
                onChangeText={(val) => {
                    setGst(val);
                    handleChange();
                }}
                style={{ borderWidth: 1, padding: 8, marginTop: 8 }}
            />
            <TextInput
                placeholder="Service Charge (%)"
                keyboardType="numeric"
                value={serviceCharge}
                onChangeText={(val) => {
                    setServiceCharge(val);
                    handleChange();
                }}
                style={{ borderWidth: 1, padding: 8, marginTop: 8 }}
            />
            <TextInput
                placeholder="Tip (fixed)"
                keyboardType="numeric"
                value={tip}
                onChangeText={(val) => {
                    setTip(val);
                    handleChange();
                }}
                style={{ borderWidth: 1, padding: 8, marginTop: 8 }}
            />
        </View>
    );
}