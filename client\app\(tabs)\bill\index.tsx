import React, { useState, useEffect } from 'react';
import { ScrollView, View, Button, Text } from 'react-native';

import PeopleInput from '@/components/ui/PeopleInput';
import ItemForm from '@/components/ui/ItemForm';
import ItemCard from '@/components/ui/ItemCard';
import GlobalChargesForm from '@/components/ui/GlobalChargesForm';

import {getHW} from "@/app/lib/api/hello-world/getHelloWorld";
import {splitBill} from "@/app/lib/api/split/getSplit";

type BillItem = {
    name: string;
    price: number;
    quantity: number;
    people: string[];
};

export default function BillScreen() {
    const [people, setPeople] = useState<string[]>([]);
    const [items, setItems] = useState<BillItem[]>([]);
    const [charges, setCharges] = useState({
        gst: 0,
        serviceCharge: 0,
        tip: 0,
    });
    const [hello, setHello] = useState<string | null>(null);


    const addItem = (item: any) => {
        setItems([...items, item]);
    };

    const subtotal = items.reduce((sum, item) => sum + item.price * item.quantity, 0);
    const gstAmt = charges.gst;
    const serviceAmt = (subtotal * charges.serviceCharge) / 100;
    const total = subtotal + gstAmt + serviceAmt + charges.tip;

    useEffect(() => {
        const fetchHello = async () => {
            try{
                const message = await getHW();
                setHello(message);
                console.log("Got message!");
            } catch (e) {
                console.error("Got error: " + e);
            }
        }
        fetchHello();
    }, []);

    const sendDataToSplitEndpoint = async () => {
        const payload = items.map(item => ({
            name: item.name,
            price: item.price,
            quantity: item.quantity,
            names: item.people
        }));

        try {
            const data = await splitBill(payload); // from client.ts
            console.log('Split response:', data);
            // Update state with result if needed
        } catch (error) {
            console.error('Error sending to split endpoint:', error);
        }
    };
    return (
        <ScrollView contentContainerStyle={{ padding: 16 }}>
            <PeopleInput people={people} setPeople={setPeople} />

            {hello && (<Text>API says: {hello}</Text>)}

            {people.length > 0 && (
                <>

                    <ItemForm people={people} onAddItem={(item: BillItem) => setItems([...items, item])} />

                    <GlobalChargesForm onChange={setCharges} />

                    {items.map((item, index) => (
                        <ItemCard key={index} item={item} />
                    ))}

                    <View style={{ marginTop: 20 }}>
                        <Text>Subtotal: ₹{subtotal.toFixed(2)}</Text>
                        <Text>GST: ₹{gstAmt.toFixed(2)}</Text>
                        <Text>Service Charge: ₹{serviceAmt.toFixed(2)}</Text>
                        <Text>Tip: ₹{charges.tip.toFixed(2)}</Text>
                        <Text style={{ fontWeight: 'bold' }}>Total: ₹{total.toFixed(2)}</Text>
                    </View>
                </>
            )}
            <Button title="Split Bill" onPress={sendDataToSplitEndpoint} />
        </ScrollView>
    );
}