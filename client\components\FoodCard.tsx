import React from 'react';
import {
    View,
    Text,
    Image,
    StyleSheet,
    TouchableOpacity,
    ImageSourcePropType,
} from 'react-native';

interface FoodCardProps {
    title: string;
    image: ImageSourcePropType;
    onPress?: () => void;
}

export default function FoodCard({
                                     title,
                                     image,
                                     onPress = () => {},
                                 }: FoodCardProps) {
    console.log(`🍔 FoodCard rendered: ${title}`);
    return (
        <TouchableOpacity style={styles.card} onPress={onPress}>
            <Image source={image} style={styles.image} />
            <Text style={styles.title}>{title}</Text>
        </TouchableOpacity>
    );
}

const styles = StyleSheet.create({
    card: {
        width: "46%",
        margin: 8,
        borderRadius: 8,
        backgroundColor: '#fff',
        elevation: 2,            // shadow on Android
        shadowColor: '#000',     // shadow on iOS
        shadowOpacity: 0.1,
        shadowRadius: 4,
        shadowOffset: { width: 0, height: 2 },
    },
    image: {
        width: '100%',
        height: 100,
        borderTopLeftRadius: 8,
        borderTopRightRadius: 8,
    },
    title: {
        padding: 8,
        fontSize: 16,
        fontWeight: '500',
    },
});