from fastapi import APIRouter
import logging

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter()

@router.get("/hw")
def hello_world():
    """Simple hello world endpoint"""
    logger.info("Triggered helloWorld")
    return "Hello World!"


@router.post("/split")
def get_split(items):
    try:
        logger.info("Starting server for the split")
    catch(e):
        