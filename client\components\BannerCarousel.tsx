import React from 'react';
import {View, Image, Dimensions, FlatList, StyleSheet} from 'react-native';
import Carousel from 'react-native-snap-carousel';
const { width } = Dimensions.get('window');
const data = [
    require('../assets/images/deal1.png'),
    require('../assets/images/deal2.png'),
    require('../assets/images/deal3.png'),
];

export default function BannerCarousel() {
    return (
        <View style={styles.container}>
            <FlatList
                data={data}
                keyExtractor={(_, idx) => idx.toString()}
                horizontal
                pagingEnabled
                showsHorizontalScrollIndicator={false}
                renderItem={({ item }) => (
                    <View style={styles.itemWrapper}>
                        <Image source={item} style={styles.image} />
                    </View>
                )}
            />
        </View>
    );
}


const styles = StyleSheet.create({
    container: {
        marginVertical: 16,
    },
    itemWrapper: {
        width,
        alignItems: 'center',
    },
    image: {
        width: width * 0.9,
        height: 150,
        borderRadius: 12,
        resizeMode: 'cover',
    },
});