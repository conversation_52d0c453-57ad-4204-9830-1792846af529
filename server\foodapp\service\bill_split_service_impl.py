import logging
from typing import List
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from service.bill_split_service import BillSplitService
from models.request import People, ItemBill
from models.response import PeopleResponse

# Configure logging
logger = logging.getLogger(__name__)


class BillSplitServiceImpl(BillSplitService):
    """
    Python equivalent of billSplitServiceImpl.java
    Implements the same bill splitting logic
    """
    
    def __init__(self):
        # public List<PeopleResponse> peopleResponseList = Collections.synchronizedList(new ArrayList<>());
        self.people_response_list: List[PeopleResponse] = []
    
    def bill_split(self, people_list: List[People]) -> List[PeopleResponse]:
        """
        Bill split implementation - exact same logic as Java version
        """
        try:
            # Clear the response list for each new request
            self.people_response_list = []
            
            # ExecutorService executorService = Executors.newFixedThreadPool(peopleList.size());
            with ThreadPoolExecutor(max_workers=len(people_list)) as executor:
                # Submit tasks for each person
                futures = []
                
                # for (People people : peopleList)
                for people in people_list:
                    # executorService.submit(() -> { ... calculateSplit(people); ... });
                    future = executor.submit(self._calculate_split_wrapper, people)
                    futures.append(future)
                
                # Wait for all tasks to complete
                for future in as_completed(futures):
                    try:
                        future.result()  # This will raise any exception that occurred
                    except Exception as e:
                        logger.error(str(e))
                        raise RuntimeError(e)
            
            # executorService.shutdown();
            # executorService.awaitTermination(10, TimeUnit.SECONDS);
            # return peopleResponseList;
            return self.people_response_list
            
        except Exception as e:
            logger.error(str(e))
            raise RuntimeError(e)
    
    def _calculate_split_wrapper(self, people: People) -> None:
        """
        Wrapper for calculate_split to handle exceptions in thread
        """
        try:
            logger.info("Bill splitting")
            self.calculate_split(people)
        except Exception as e:
            logger.error(str(e))
            raise RuntimeError(e)
    
    def calculate_split(self, people: People) -> None:
        """
        Calculate split for a person - exact same logic as Java version
        """
        try:
            # PeopleResponse peopleResponse = new PeopleResponse();
            people_response = PeopleResponse(name="", contri=0, items=[])
            
            # List<ItemBill> items = people.getItems();
            items = people.items
            
            # int contri = 0;
            contri = 0
            
            # for (ItemBill itemBill : items)
            for item_bill in items:
                # int split = (itemBill.getPrice() * itemBill.getQuantity()) / itemBill.getSplit();
                split = (item_bill.price * item_bill.quantity) // item_bill.split
                # contri += split;
                contri += split
            
            # peopleResponse.setName(people.getName());
            # peopleResponse.setContri(contri);
            # peopleResponse.setItems(items);
            people_response.name = people.name
            people_response.contri = contri
            people_response.items = items
            
            # peopleResponseList.add(peopleResponse);
            self.people_response_list.append(people_response)
            
        except Exception as e:
            logger.error(str(e))
            raise RuntimeError(e)
