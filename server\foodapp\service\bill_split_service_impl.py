import logging
from typing import List, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
from service.bill_split_service import BillSplitService
from models.request import People, ItemBill, TaxInfo
from models.response import PeopleResponse

# Configure logging
logger = logging.getLogger(__name__)


class BillSplitServiceImpl(BillSplitService):
    """
    Python equivalent of billSplitServiceImpl.java
    Implements the same bill splitting logic
    """
    
    def __init__(self):
        # public List<PeopleResponse> peopleResponseList = Collections.synchronizedList(new ArrayList<>());
        self.people_response_list: List[PeopleResponse] = []
        # Store tax info for use in calculate_split
        self.current_tax_info: Optional[TaxInfo] = None

    def bill_split(self, people_list: List[People], tax_info: Optional[TaxInfo] = None) -> List[PeopleResponse]:
        """
        Bill split implementation - exact same logic as Java version
        Now with added tax and tip calculation
        """
        try:
            # Clear the response list for each new request
            self.people_response_list = []
            # Store tax info for use in calculate_split
            self.current_tax_info = tax_info
            
            # ExecutorService executorService = Executors.newFixedThreadPool(peopleList.size());
            with ThreadPoolExecutor(max_workers=len(people_list)) as executor:
                # Submit tasks for each person
                futures = []
                
                # for (People people : peopleList)
                for people in people_list:
                    # executorService.submit(() -> { ... calculateSplit(people); ... });
                    future = executor.submit(self._calculate_split_wrapper, people)
                    futures.append(future)
                
                # Wait for all tasks to complete
                for future in as_completed(futures):
                    try:
                        future.result()  # This will raise any exception that occurred
                    except Exception as e:
                        logger.error(str(e))
                        raise RuntimeError(e)
            
            # executorService.shutdown();
            # executorService.awaitTermination(10, TimeUnit.SECONDS);

            # NEW SECTION: Apply taxes and tips after main calculation
            if tax_info:
                self._apply_taxes_and_tips()

            # return peopleResponseList;
            return self.people_response_list
            
        except Exception as e:
            logger.error(str(e))
            raise RuntimeError(e)
    
    def _calculate_split_wrapper(self, people: People) -> None:
        """
        Wrapper for calculate_split to handle exceptions in thread
        """
        try:
            logger.info("Bill splitting")
            self.calculate_split(people)
        except Exception as e:
            logger.error(str(e))
            raise RuntimeError(e)
    
    def calculate_split(self, people: People) -> None:
        """
        Calculate split for a person - exact same logic as Java version
        Now with added tax and tip calculation
        """
        try:
            # PeopleResponse peopleResponse = new PeopleResponse();
            people_response = PeopleResponse(name="", contri=0, items=[])

            # List<ItemBill> items = people.getItems();
            items = people.items

            # int contri = 0;
            contri = 0

            # EXISTING LOGIC - DO NOT TOUCH
            # for (ItemBill itemBill : items)
            for item_bill in items:
                # int split = (itemBill.getPrice() * itemBill.getQuantity()) / itemBill.getSplit();
                split = (item_bill.price * item_bill.quantity) // item_bill.split
                # contri += split;
                contri += split

            # NOTE: Tax and tip calculation will be done after all people are processed

            # peopleResponse.setName(people.getName());
            # peopleResponse.setContri(contri);
            # peopleResponse.setItems(items);
            people_response.name = people.name
            people_response.contri = contri
            people_response.items = items

            # peopleResponseList.add(peopleResponse);
            self.people_response_list.append(people_response)

        except Exception as e:
            logger.error(str(e))
            raise RuntimeError(e)

    def _apply_taxes_and_tips(self) -> None:
        """
        Apply taxes and tips to all people after main bill calculation is complete
        This ensures we have the complete bill total before calculating percentages
        """
        try:
            if not self.current_tax_info or not self.people_response_list:
                return

            tax_info = self.current_tax_info
            total_people = len(self.people_response_list)

            # Calculate total bill amount (before taxes)
            total_bill_amount = sum(response.contri for response in self.people_response_list)

            if total_bill_amount == 0:
                logger.warning("Total bill amount is 0, skipping tax calculation")
                return

            logger.info(f"Applying taxes and tips: Total bill={total_bill_amount}, People={total_people}")
            logger.info(f"Tax info: GST={tax_info.gst}, Service={tax_info.service_charge}%, Tip={tax_info.tip}")

            # Calculate total service charge amount
            total_service_charge = (total_bill_amount * tax_info.service_charge) / 100

            # Apply taxes and tips to each person
            for response in self.people_response_list:
                # Calculate this person's percentage of the total bill
                person_percentage = response.contri / total_bill_amount

                # GST: Fixed amount split equally among all people
                gst_share = tax_info.gst / total_people

                # Service charge: Percentage-based on person's contribution
                service_charge_share = total_service_charge * person_percentage

                # Tip: Fixed amount split equally among all people
                tip_share = tax_info.tip / total_people

                # Add taxes and tips to person's contribution
                tax_tip_total = int(gst_share + service_charge_share + tip_share)
                response.contri += tax_tip_total

                logger.info(f"{response.name}: Base={response.contri - tax_tip_total}, "
                          f"GST={gst_share:.2f}, Service={service_charge_share:.2f}, "
                          f"Tip={tip_share:.2f}, Final={response.contri}")

        except Exception as e:
            logger.error(f"Error applying taxes and tips: {str(e)}")
            # Don't raise exception, just log error to avoid breaking the main flow
