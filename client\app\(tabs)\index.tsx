import React from 'react';
import { ScrollView, View } from 'react-native';
import AppHeader from '@/components/AppHeader';
import SearchBar from '@/components/SearchBar';
import BannerCarousel from '@/components/BannerCarousel';
import SectionHeader from "@/components/SectionHeader";
import FoodCard from "@/components/FoodCard";

export default function Home() {
    return (
        <ScrollView style={{ flex: 1 , marginTop: 40}}>
            <AppHeader />
            <SearchBar />
            <BannerCarousel />

            <SectionHeader title="Your trusted picks" onPress={() => console.log('View all pressed')} />
            <View style={{ flexDirection: 'row', justifyContent: 'space-around', marginHorizontal: 14 }}>
                {/* Repeat FoodCard with different props */}
                <FoodCard title="Chicken Burger" image={require('../../assets/images/burger1.png')} />
                <FoodCard title="BBQ Burger"     image={require('../../assets/images/burger2.png')} />
            </View>

            {/* …and so on for Recommended, etc. */}
        </ScrollView>
    );
}