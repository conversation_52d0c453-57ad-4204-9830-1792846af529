import React, { useState } from 'react';
import { View, TextInput, Button, Text } from 'react-native';

export default function PeopleInput({ people, setPeople }: any) {
    const [name, setName] = useState('');

    const addPerson = () => {
        if (name.trim()) {
            setPeople([...people, name.trim()]);
            setName('');
        }
    };

    return (
        <View style={{ marginBottom: 16 }}>
            <Text style={{ fontSize: 16, marginBottom: 4 }}>Enter People Involved:</Text>
            <TextInput
                placeholder="Name"
                value={name}
                onChangeText={setName}
                style={{ borderWidth: 1, padding: 8, marginBottom: 8 }}
            />
            <Button title="Add Person" onPress={addPerson} />
            <View style={{ marginTop: 10 }}>
                {people.map((p: string, i: number) => (
                    <Text key={i}>• {p}</Text>
                ))}
            </View>
        </View>
    );
}