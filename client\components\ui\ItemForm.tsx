import React, { useState } from 'react';
import { View, TextInput, Button, Text } from 'react-native';
import PersonSelector from './PersonSelector';

export default function ItemForm({ people, onAddItem }: any) {
    const [itemName, setItemName] = useState('');
    const [price, setPrice] = useState('');
    const [quantity, setQuantity] = useState('');
    const [selectedPeople, setSelectedPeople] = useState<string[]>([]);

    const handleSubmit = () => {
        if (!price || !quantity || selectedPeople.length === 0) return;

        const item = {
            name: itemName,
            price: parseFloat(price),
            quantity: parseInt(quantity),
            people: selectedPeople
        };

        onAddItem(item);
        setItemName('');
        setPrice('');
        setQuantity('');
        setSelectedPeople([]);
    };

    return (
        <View style={{ marginVertical: 16 }}>
            <Text style={{ fontWeight: 'bold' }}>Add Item</Text>
            <TextInput placeholder="Item Name" value={itemName} onChangeText={setItemName} style={{ borderWidth: 1, padding: 8, marginTop: 8 }} />
            <TextInput placeholder="Price" keyboardType="numeric" value={price} onChangeText={setPrice} style={{ borderWidth: 1, padding: 8, marginTop: 8 }} />
            <TextInput placeholder="Quantity" keyboardType="numeric" value={quantity} onChangeText={setQuantity} style={{ borderWidth: 1, padding: 8, marginTop: 8 }} />
            <PersonSelector people={people} selected={selectedPeople} onChange={setSelectedPeople} />
            <Button title="Add Item" onPress={handleSubmit} />
        </View>
    );
}