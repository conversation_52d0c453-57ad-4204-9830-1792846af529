from abc import ABC, abstractmethod
from typing import List, Optional
from models.request import People, TaxInfo
from models.response import PeopleResponse


class BillSplitService(ABC):
    """
    Python equivalent of BillSplitService.java interface
    """

    @abstractmethod
    def bill_split(self, people_list: List[People], tax_info: Optional[TaxInfo] = None) -> List[PeopleResponse]:
        """
        Abstract method for bill splitting with optional tax information
        """
        pass
