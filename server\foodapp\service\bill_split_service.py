from abc import ABC, abstractmethod
from typing import List
from models.request import People
from models.response import PeopleResponse


class BillSplitService(ABC):
    """
    Python equivalent of BillSplitService.java interface
    """
    
    @abstractmethod
    def bill_split(self, people_list: List[People]) -> List[PeopleResponse]:
        """
        Abstract method for bill splitting
        """
        pass
