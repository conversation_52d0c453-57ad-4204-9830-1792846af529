import React from 'react';
import { View, Text } from 'react-native';

export default function ItemCard({ item }: any) {
    const total =
        (item.price * item.quantity).toFixed(2);

    return (
        <View style={{ borderWidth: 1, padding: 12, marginVertical: 6, borderRadius: 8, backgroundColor: '#fff', elevation: 2 }}>
            <Text style={{ fontWeight: 'bold' }}>{item.name || 'Unnamed Item'}</Text>
            <Text>Price: ${item.price} x {item.quantity}</Text>
            {item.serviceCharge ? <Text>Service Charge: ${item.serviceCharge}</Text> : null}
            <Text>Total: ${total}</Text>
            <Text>Shared by: {item.people.join(', ')}</Text>
        </View>
    );
}