// app/_layout.tsx
import 'react-native-gesture-handler';
import 'react-native-reanimated';
import { ThemeProvider, DefaultTheme, DarkTheme } from '@react-navigation/native';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import {useColorScheme} from '@/hooks/useColorScheme';

export default function RootLayout() {
    const scheme = useColorScheme();
    return (
        <ThemeProvider value={scheme === 'dark' ? DarkTheme : DefaultTheme}>
            <Stack>
                <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
                <Stack.Screen name="+not-found" options={{ title: 'Page Not Found' }} />
            </Stack>
            <StatusBar style="auto" />
        </ThemeProvider>
    );
}