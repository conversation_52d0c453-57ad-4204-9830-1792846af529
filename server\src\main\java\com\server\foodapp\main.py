from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from routes.hello_world import router as hello_world_router

app = FastAPI(title="FoodApp")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(hello_world_router)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app:app", host="0.0.0.0", port=8080, reload=True)
