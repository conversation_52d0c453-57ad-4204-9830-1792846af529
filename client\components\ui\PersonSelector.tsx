import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';

export default function PersonSelector({ people, selected, onChange }: any) {
    const toggleSelect = (person: string) => {
        if (selected.includes(person)) {
            onChange(selected.filter((p: string) => p !== person));
        } else {
            onChange([...selected, person]);
        }
    };

    return (
        <View style={{ marginVertical: 12 }}>
            <Text style={{ marginBottom: 4 }}>Select People:</Text>
            <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>
                {people.map((person: string, index: number) => (
                    <TouchableOpacity
                        key={index}
                        onPress={() => toggleSelect(person)}
                        style={{
                            padding: 8,
                            margin: 4,
                            borderWidth: 1,
                            borderRadius: 6,
                            backgroundColor: selected.includes(person) ? '#cce5ff' : '#f8f9fa'
                        }}
                    >
                        <Text>{person}</Text>
                    </TouchableOpacity>
                ))}
            </View>
        </View>
    );
}