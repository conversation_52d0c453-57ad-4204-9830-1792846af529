package com.server.foodapp.service.impl;

import com.server.foodapp.models.request.ItemBill;
import com.server.foodapp.models.request.People;
import com.server.foodapp.models.response.PeopleResponse;
import com.server.foodapp.service.BillSplitService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class billSplitServiceImpl implements BillSplitService {

    public List<PeopleResponse> peopleResponseList = Collections.synchronizedList(new ArrayList<>());

    @Override
    public List<PeopleResponse> billSplit(List<People> peopleList) {
        try{
            ExecutorService executorService = Executors.newFixedThreadPool(peopleList.size());

            for (People people : peopleList) {
                executorService.submit(() -> {
                    try{
                        log.info("Bill splitting");
                        calculateSplit(people);
                    } catch (Exception e) {
                        log.error(e.getMessage());
                        throw new RuntimeException(e);
                    }
                });
            }
            executorService.shutdown();
            executorService.awaitTermination(10, TimeUnit.SECONDS);
            return peopleResponseList;
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new RuntimeException(e);
        }
    }

    public void calculateSplit(People people) {
        try{
            PeopleResponse peopleResponse = new PeopleResponse();
            List<ItemBill> items = people.getItems();
            int contri = 0;
            for (ItemBill itemBill : items) {
                int split = (itemBill.getPrice() * itemBill.getQuantity()) / itemBill.getSplit();
                contri += split;
            }
            peopleResponse.setName(people.getName());
            peopleResponse.setContri(contri);
            peopleResponse.setItems(items);
            peopleResponseList.add(peopleResponse);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new RuntimeException(e);
        }
    }


}
