import React from 'react';
import { View, TextInput, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

export default function SearchBar() {
    return (
        <View style={styles.wrapper}>
            <Ionicons name="search" size={20} style={styles.icon} />
            <TextInput
                style={styles.input}
                placeholder="Search..."
                onChangeText={text => console.log('Searching for', text)}
            />
            <TouchableOpacity>
                <Ionicons name="mic" size={20} />
            </TouchableOpacity>
        </View>
    );
}

const styles = StyleSheet.create({
    wrapper: {
        flexDirection: 'row',
        backgroundColor: '#efefef',
        borderRadius: 8,
        paddingHorizontal: 12,
        paddingVertical: 8,
        alignItems: 'center',
        marginHorizontal: 16,
        borderStyle: 'solid',
        borderWidth: 1,
        borderColor: 'gray',
    },
    icon: { marginRight: 8, color: '#888' },
    input: { flex: 1, fontSize: 16 },
});