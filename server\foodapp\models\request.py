from pydantic import BaseModel
from typing import List, Optional


class TaxInfo(BaseModel):
    """
    Tax and tip information from frontend
    """
    gst: float = 0.0  # GST amount (fixed)
    service_charge: float = 0.0  # Service charge percentage
    tip: float = 0.0  # Tip amount (fixed)


class BillSplitRequest(BaseModel):
    """
    Complete bill split request with items and tax info
    """
    items: List['ItemApi']
    tax_info: Optional[TaxInfo] = None


class ItemApi(BaseModel):
    """
    Equivalent to ItemApi.java
    """
    name: str
    price: int
    quantity: int
    names: List[str]


class ItemBill(BaseModel):
    """
    Equivalent to ItemBill.java
    """
    name: str
    price: int
    quantity: int
    split: int


class People(BaseModel):
    """
    Equivalent to People.java
    """
    name: str
    items: List[ItemBill]
