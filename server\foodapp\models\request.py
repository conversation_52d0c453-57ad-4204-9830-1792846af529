from pydantic import BaseModel
from typing import List


class ItemApi(BaseModel):
    """
    Equivalent to ItemApi.java
    """
    name: str
    price: int
    quantity: int
    names: List[str]


class ItemBill(BaseModel):
    """
    Equivalent to ItemBill.java
    """
    name: str
    price: int
    quantity: int
    split: int


class People(BaseModel):
    """
    Equivalent to People.java
    """
    name: str
    items: List[ItemBill]
