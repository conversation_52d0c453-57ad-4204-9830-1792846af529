from fastapi import APIRouter, HTTPException
from typing import List
import logging

# Import the models and services
from models.request import BillSplitRequest
from models.response import PeopleResponse
from dto.item_to_people import ItemToPeople
from service.bill_split_service_impl import BillSplitServiceImpl

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter()

# Initialize services (equivalent to @Autowired in Java)
bill_split_service = BillSplitServiceImpl()
item_to_people = ItemToPeople()

@router.get("/hw")
def hello_world():
    """Simple hello world endpoint"""
    logger.info("Triggered helloWorld")
    return "Hello World!"


@router.post("/split")
def get_split(request: BillSplitRequest) -> List[PeopleResponse]:
    """
    Python equivalent of BillSplitController.getSplit()
    Now enhanced with GST, service charge, and tip calculation
    Supports both old format (items only) and new format (items + tax_info)
    """
    try:
        # log.info("Starting server for split");
        logger.info("Starting server for split")

        # Extract items and tax info from request
        items = request.items
        tax_info = request.tax_info

        if tax_info:
            logger.info(f"Tax info received: {tax_info}")

        # List<People> people = itemToPeople.convertItemsToPeople(items);
        people = item_to_people.convert_items_to_people(items)

        # List<PeopleResponse> respone = billSplitService.billSplit(people);
        response = bill_split_service.bill_split(people, tax_info)

        # log.info("Finished server for split: {}", respone);
        logger.info(f"Finished server for split: {response}")

        # return respone;
        return response

    except Exception as e:
        # log.error(e.getMessage());
        logger.error(str(e))
        # throw new RuntimeException(e);
        raise HTTPException(status_code=500, detail=str(e))