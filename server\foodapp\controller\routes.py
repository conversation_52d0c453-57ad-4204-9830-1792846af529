from fastapi import APIRouter, HTTPException
from typing import List
import logging

# Import the models and services
from models.request import Item<PERSON><PERSON>, BillSplitRequest
from models.response import PeopleResponse
from dto.item_to_people import ItemToPeople
from service.bill_split_service_impl import BillSplitServiceImpl

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter()

# Initialize services (equivalent to @Autowired in Java)
bill_split_service = BillSplitServiceImpl()
item_to_people = ItemToPeople()

@router.get("/hw")
def hello_world():
    """Simple hello world endpoint"""
    logger.info("Triggered helloWorld")
    return "Hello World!"


@router.post("/split")
def get_split(items: List[ItemApi]) -> List[PeopleResponse]:
    """
    Python equivalent of BillSplitController.getSplit()
    Exact same logic as the Java version - BACKWARD COMPATIBILITY
    """
    try:
        # log.info("Starting server for split");
        logger.info("Starting server for split")

        # List<People> people = itemToPeople.convertItemsToPeople(items);
        people = item_to_people.convert_items_to_people(items)

        # List<PeopleResponse> respone = billSplitService.billSplit(people);
        response = bill_split_service.bill_split(people)

        # log.info("Finished server for split: {}", respone);
        logger.info(f"Finished server for split: {response}")

        # return respone;
        return response

    except Exception as e:
        # log.error(e.getMessage());
        logger.error(str(e))
        # throw new RuntimeException(e);
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/split-with-taxes")
def get_split_with_taxes(request: BillSplitRequest) -> List[PeopleResponse]:
    """
    Enhanced bill split endpoint that includes GST, service charge, and tip calculation
    """
    try:
        logger.info("Starting server for split with taxes")
        logger.info(f"Tax info: {request.tax_info}")

        # Convert items to people (same logic as before)
        people = item_to_people.convert_items_to_people(request.items)

        # Bill split with tax information
        response = bill_split_service.bill_split(people, request.tax_info)

        logger.info(f"Finished server for split with taxes: {response}")
        return response

    except Exception as e:
        logger.error(str(e))
        raise HTTPException(status_code=500, detail=str(e))