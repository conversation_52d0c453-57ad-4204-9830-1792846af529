from fastapi import APIRouter, HTTPException
from typing import List, Union
import logging

# Import the models and services
from models.request import BillSplitRequest, ItemApi, TaxInfo
from models.response import PeopleResponse
from dto.item_to_people import ItemToPeople
from service.bill_split_service_impl import BillSplitServiceImpl

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter()

# Initialize services (equivalent to @Autowired in Java)
bill_split_service = BillSplitServiceImpl()
item_to_people = ItemToPeople()

@router.get("/hw")
def hello_world():
    """Simple hello world endpoint"""
    logger.info("Triggered helloWorld")
    return "Hello World!"


@router.post("/split")
def get_split_legacy(items: List[ItemApi]) -> List[PeopleResponse]:
    """
    LEGACY: Python equivalent of BillSplitController.getSplit()
    Exact same logic as the Java version - BACKWARD COMPATIBILITY
    This endpoint maintains the original format for existing frontend
    """
    try:
        # log.info("Starting server for split");
        logger.info("Starting server for split (legacy format)")

        # List<People> people = itemToPeople.convertItemsToPeople(items);
        people = item_to_people.convert_items_to_people(items)

        # List<PeopleResponse> respone = billSplitService.billSplit(people);
        response = bill_split_service.bill_split(people, None)

        # log.info("Finished server for split: {}", respone);
        logger.info(f"Finished server for split: {response}")

        # return respone;
        return response

    except Exception as e:
        # log.error(e.getMessage());
        logger.error(str(e))
        # throw new RuntimeException(e);
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/split-enhanced")
def get_split_enhanced(request: BillSplitRequest) -> List[PeopleResponse]:
    """
    ENHANCED: Bill split with GST, service charge, and tip calculation
    Use this endpoint when you want to include tax calculations
    """
    try:
        logger.info("Starting server for enhanced split with taxes")

        # Extract items and tax info from request
        items = request.items
        tax_info = request.tax_info

        if tax_info:
            logger.info(f"Tax info received: {tax_info}")

        # List<People> people = itemToPeople.convertItemsToPeople(items);
        people = item_to_people.convert_items_to_people(items)

        # List<PeopleResponse> respone = billSplitService.billSplit(people);
        response = bill_split_service.bill_split(people, tax_info)

        # log.info("Finished server for split: {}", respone);
        logger.info(f"Finished server for enhanced split: {response}")

        # return respone;
        return response

    except Exception as e:
        # log.error(e.getMessage());
        logger.error(str(e))
        # throw new RuntimeException(e);
        raise HTTPException(status_code=500, detail=str(e))