from fastapi import APIRouter, HTTPException, Request
from typing import List, Union, Any
import logging
import json

# Import the models and services
from models.request import BillSplitRequest, ItemApi, TaxInfo
from models.response import PeopleResponse
from dto.item_to_people import ItemToPeople
from service.bill_split_service_impl import BillSplitServiceImpl

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter()

# Initialize services (equivalent to @Autowired in Java)
bill_split_service = BillSplitServiceImpl()
item_to_people = ItemToPeople()

@router.get("/hw")
def hello_world():
    """Simple hello world endpoint"""
    logger.info("Triggered helloWorld")
    return "Hello World!"


@router.post("/split")
async def get_split(request: Request) -> List[PeopleResponse]:
    """
    Python equivalent of BillSplitController.getSplit()
    Enhanced to support both old format (List[ItemApi]) and new format (BillSplitRequest with taxes)
    Automatically detects the format and handles accordingly
    """
    try:
        logger.info("Starting server for split")

        # Get raw JSON data
        raw_data = await request.json()

        # Detect request format and extract data
        if isinstance(raw_data, list):
            # Old format: List[ItemApi]
            logger.info("Detected legacy format (List[ItemApi])")
            items = [ItemApi(**item) for item in raw_data]
            tax_info = None
        elif isinstance(raw_data, dict) and 'items' in raw_data:
            # New format: BillSplitRequest
            logger.info("Detected new format (BillSplitRequest)")
            items = [ItemApi(**item) for item in raw_data['items']]
            tax_info_dict = raw_data.get('tax_info')
            tax_info = TaxInfo(**tax_info_dict) if tax_info_dict else None
            if tax_info:
                logger.info(f"Tax info received: {tax_info}")
        else:
            raise ValueError("Invalid request format. Expected List[ItemApi] or BillSplitRequest")

        # List<People> people = itemToPeople.convertItemsToPeople(items);
        people = item_to_people.convert_items_to_people(items)

        # List<PeopleResponse> respone = billSplitService.billSplit(people);
        response = bill_split_service.bill_split(people, tax_info)

        # log.info("Finished server for split: {}", respone);
        logger.info(f"Finished server for split: {response}")

        # return respone;
        return response

    except Exception as e:
        # log.error(e.getMessage());
        logger.error(str(e))
        # throw new RuntimeException(e);
        raise HTTPException(status_code=500, detail=str(e))