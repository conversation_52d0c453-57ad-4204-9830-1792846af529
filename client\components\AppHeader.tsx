import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

export default function AppHeader() {
    return (
        <View style={styles.container}>
            <View>
                <Text style={styles.addressLabel}>Deliver to</Text>
                <Text style={styles.address}>15 Water Street, Fremont</Text>
            </View>
            <TouchableOpacity style={styles.iconButton}>
                <Ionicons name="notifications-outline" size={24} />
            </TouchableOpacity>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        padding: 16,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    addressLabel: { fontSize: 12, color: '#666' },
    address: { fontSize: 16, fontWeight: '600' },
    iconButton: {
        padding: 8,
        borderRadius: 24,
        backgroundColor: '#f0f0f0',
    },
});