package com.server.foodapp.dto;

import com.server.foodapp.models.request.ItemApi;
import com.server.foodapp.models.request.ItemBill;
import com.server.foodapp.models.request.People;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class ItemToPeople {

    public List<People> convertItemsToPeople(List<ItemApi> items){
        try{
            log.info("Converting Items to People");
            Map<String, List<ItemApi>> peopleItems = new HashMap<>();
            for (ItemApi itemApi : items) {
                for (String name : itemApi.getNames()) {
                    peopleItems
                            .computeIfAbsent(name, k -> new ArrayList<>())
                            .add(itemApi);
                }
            }
            List<People> peopleList = Collections.synchronizedList(new ArrayList<>());
            ExecutorService executorService = Executors.newFixedThreadPool(peopleItems.size());
            for(String name : peopleItems.keySet()){
                executorService.submit(() -> {
                    try{
                        People people = new People();
                        List<ItemBill> itemBills = Collections.synchronizedList(new ArrayList<>());
                        for(ItemApi item : peopleItems.get(name)){
                            try{
                                ItemBill itemBill = new ItemBill();
                                itemBill.setName(item.getName());
                                itemBill.setPrice(item.getPrice());
                                itemBill.setQuantity(item.getQuantity());
                                itemBill.setSplit(item.getNames().size());
                                itemBills.add(itemBill);
                            } catch (Exception e) {
                                throw new RuntimeException(e);
                            }
                        }
                        people.setName(name);
                        people.setItems(itemBills);
                        peopleList.add(people);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                });
            }
            executorService.shutdown();
            executorService.awaitTermination(10, TimeUnit.SECONDS);
            return peopleList;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
