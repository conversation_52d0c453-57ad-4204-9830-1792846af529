package com.server.foodapp.controller;

import com.server.foodapp.dto.ItemToPeople;
import com.server.foodapp.models.request.ItemApi;
import com.server.foodapp.models.request.People;
import com.server.foodapp.models.response.PeopleResponse;
import com.server.foodapp.service.BillSplitService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
public class BillSplitController {

    @Autowired
    private final BillSplitService billSplitService;

    @Autowired
    private final ItemToPeople itemToPeople;

    public BillSplitController(BillSplitService billSplitService, ItemToPeople itemToPeople, ItemToPeople itemToPeople1) {
        this.billSplitService = billSplitService;
        this.itemToPeople = itemToPeople1;
    }

    @PostMapping(value = "/split")
    public List<PeopleResponse> getSplit(@RequestBody List<ItemApi> items) {
        try{
            log.info("Starting server for split");
            List<People> people = itemToPeople.convertItemsToPeople(items);
            List<PeopleResponse> respone = billSplitService.billSplit(people);
            log.info("Finished server for split: {}", respone);
            return respone;
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new RuntimeException(e);
        }
    }

}
