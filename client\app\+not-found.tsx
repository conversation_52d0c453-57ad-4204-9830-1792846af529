// app/+not-found.tsx
import React from 'react';
import { View, Text, StyleSheet, Button } from 'react-native';
import { useRouter } from 'expo-router';
import { StatusBar } from 'expo-status-bar';

export default function NotFound() {
    const router = useRouter();

    return (
        <View style={styles.container}>
            <StatusBar style="auto" />
            <Text style={styles.code}>404</Text>
            <Text style={styles.message}>Page Not Found</Text>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center'
    },
    code: {
        fontSize: 64,
        fontWeight: 'bold',
        marginBottom: 16
    },
    message: {
        fontSize: 18,
        marginBottom: 24
    }
});