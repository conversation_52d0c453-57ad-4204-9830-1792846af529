import logging
from typing import List, Dict
from concurrent.futures import ThreadPoolExecutor, as_completed
from collections import defaultdict
from models.request import Item<PERSON><PERSON>, Item<PERSON>ill, People

# Configure logging
logger = logging.getLogger(__name__)


class ItemToPeople:
    """
    Python equivalent of ItemToPeople.java
    Converts ItemApi list to People list with the same logic
    """
    
    def convert_items_to_people(self, items: List[ItemApi]) -> List[People]:
        """
        Converts Items to People - exact same logic as Java version
        """
        try:
            logger.info("Converting Items to People")
            
            # Map<String, List<ItemApi>> peopleItems = new HashMap<>();
            people_items: Dict[str, List[ItemApi]] = defaultdict(list)
            
            # for (ItemApi itemApi : items)
            for item_api in items:
                # for (String name : itemApi.getNames())
                for name in item_api.names:
                    # peopleItems.computeIfAbsent(name, k -> new ArrayList<>()).add(itemApi);
                    people_items[name].append(item_api)
            
            # List<People> peopleList = Collections.synchronizedList(new ArrayList<>());
            people_list: List[People] = []
            
            # ExecutorService executorService = Executors.newFixedThreadPool(peopleItems.size());
            with ThreadPoolExecutor(max_workers=len(people_items)) as executor:
                # Submit tasks for each person
                future_to_name = {}
                
                # for(String name : peopleItems.keySet())
                for name in people_items.keys():
                    future = executor.submit(self._process_person, name, people_items[name])
                    future_to_name[future] = name
                
                # Wait for all tasks to complete and collect results
                for future in as_completed(future_to_name):
                    try:
                        person = future.result()
                        people_list.append(person)
                    except Exception as e:
                        logger.error(f"Error processing person {future_to_name[future]}: {str(e)}")
                        raise RuntimeError(e)
            
            return people_list
            
        except Exception as e:
            logger.error(f"Error in convert_items_to_people: {str(e)}")
            raise RuntimeError(e)
    
    def _process_person(self, name: str, person_items: List[ItemApi]) -> People:
        """
        Process a single person - equivalent to the lambda function in Java
        """
        try:
            # People people = new People();
            people = People(name="", items=[])
            
            # List<ItemBill> itemBills = Collections.synchronizedList(new ArrayList<>());
            item_bills: List[ItemBill] = []
            
            # for(ItemApi item : peopleItems.get(name))
            for item in person_items:
                try:
                    # ItemBill itemBill = new ItemBill();
                    item_bill = ItemBill(
                        name=item.name,           # itemBill.setName(item.getName());
                        price=item.price,         # itemBill.setPrice(item.getPrice());
                        quantity=item.quantity,   # itemBill.setQuantity(item.getQuantity());
                        split=len(item.names)     # itemBill.setSplit(item.getNames().size());
                    )
                    # itemBills.add(itemBill);
                    item_bills.append(item_bill)
                except Exception as e:
                    raise RuntimeError(e)
            
            # people.setName(name);
            # people.setItems(itemBills);
            people.name = name
            people.items = item_bills
            
            return people
            
        except Exception as e:
            raise RuntimeError(e)
